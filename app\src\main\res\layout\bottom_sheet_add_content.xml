<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:paddingTop="16dp"
    android:paddingBottom="32dp"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">

    <!-- Handle bar -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:background="#40FFFFFF"
        android:layout_marginBottom="24dp" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Add Content"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="24dp"
        android:fontFamily="sans-serif-medium" />

    <!-- Image Option -->
    <LinearLayout
        android:id="@+id/imageOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?attr/selectableItemBackground"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginBottom="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_image"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Image"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:fontFamily="sans-serif" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="#AAAAAA" />

    </LinearLayout>

    <!-- Document Option -->
    <LinearLayout
        android:id="@+id/documentOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?attr/selectableItemBackground"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginBottom="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_document"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Document"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:fontFamily="sans-serif" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="#AAAAAA" />

    </LinearLayout>

    <!-- Camera Option -->
    <LinearLayout
        android:id="@+id/cameraOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?attr/selectableItemBackground"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_camera"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Camera"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:fontFamily="sans-serif" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="#AAAAAA" />

    </LinearLayout>

</LinearLayout>
