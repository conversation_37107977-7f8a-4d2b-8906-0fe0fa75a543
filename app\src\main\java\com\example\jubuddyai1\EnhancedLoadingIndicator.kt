package com.example.jubuddyai1

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView

class EnhancedLoadingIndicator(private val context: Context) {
    private val loadingDialog: Dialog = Dialog(context)
    private var loadingText: TextView? = null
    private var stopButton: Button? = null
    private var onStopGenerationListener: OnStopGenerationListener? = null
    
    interface OnStopGenerationListener {
        fun onStopGeneration()
    }
    
    init {
        setupDialog()
    }
    
    private fun setupDialog() {
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setPadding(60, 40, 60, 40)
            
            // Add progress bar
            addView(ProgressBar(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    gravity = Gravity.CENTER
                    bottomMargin = 20
                }
            })
            
            // Add loading text
            loadingText = TextView(context).apply {
                text = "Loading..."
                setTextColor(Color.WHITE)
                textSize = 16f
                gravity = Gravity.CENTER
                setPadding(0, 0, 0, 20)
            }
            addView(loadingText)
            
            // Add stop button
            stopButton = Button(context).apply {
                text = "Stop Generation"
                setBackgroundColor(Color.parseColor("#FF6B6B"))
                setTextColor(Color.WHITE)
                setPadding(30, 15, 30, 15)
                visibility = View.GONE
                
                setOnClickListener {
                    onStopGenerationListener?.onStopGeneration()
                    dismiss()
                }
            }
            addView(stopButton)
        }
        
        loadingDialog.apply {
            setContentView(layout)
            window?.apply {
                setBackgroundDrawable(ColorDrawable(Color.parseColor("#CC000000")))
                attributes = attributes.apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    gravity = Gravity.CENTER
                }
            }
            setCancelable(false)
        }
    }
    
    fun setLoadingText(text: String) {
        loadingText?.text = text
    }
    
    fun showWithStopButton(showStopButton: Boolean) {
        stopButton?.visibility = if (showStopButton) View.VISIBLE else View.GONE
        show()
    }
    
    fun show() {
        if (!loadingDialog.isShowing()) {
            loadingDialog.show()
        }
    }
    
    fun dismiss() {
        if (loadingDialog.isShowing()) {
            loadingDialog.dismiss()
        }
    }
    
    fun setOnStopGenerationListener(listener: OnStopGenerationListener) {
        this.onStopGenerationListener = listener
    }
}
